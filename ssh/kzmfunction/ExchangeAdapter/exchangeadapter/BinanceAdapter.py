# region =======================================备注说明
"""
备注说明：
    现货官方sdk:
    https://github.com/binance/binance-connector-python
    pip install binance-connector
    期货官方sdk:
    https://github.com/binance/binance-futures-connector-python
    pip install binance-futures-connector
    币安借币官方sdk:
    https://github.com/binance/binance-connector-python/tree/master/clients/crypto_loan
    pip install binance-sdk-crypto-loan
"""
# endregion =======================================备注说明


# region =======================================import
from traceback import format_exc
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .ExchangeAdapter import ExchangeAdapter
from ..api_config import binance_api_config
from math import log, ceil
from time import sleep, time
from binance.um_futures import UMFutures
from binance.spot import Spot
import pandas as pd
from datetime import timedelta

from binance_common.configuration import ConfigurationRestAPI
from binance_common.constants import CRYPTO_LOAN_REST_API_PROD_URL
from binance_sdk_crypto_loan.crypto_loan import CryptoLoan
# 备用方案：直接HTTP请求
import requests
import hmac
import hashlib
from urllib.parse import urlencode

# endregion =======================================import


class BinanceAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'):
        self._api_config = binance_api_config.api_config
        self.account = account
        self.exchange_name = 'binance'

        # 获取API密钥
        if account:
            self.api_key = self._api_config[account]['api_key']
            self.api_secret = self._api_config[account]['secret_key']
        else:
            self.api_key = self._api_config['查询']['api_key']
            self.api_secret = self._api_config['查询']['secret_key']

        # 创建交易所
        self.spot_exchange = Spot(api_key=self.api_key, api_secret=self.api_secret)
        self.swap_exchange = UMFutures(key=self.api_key, secret=self.api_secret)

        configuration = ConfigurationRestAPI(
            api_key=self.api_key,
            api_secret=self.api_secret,
            base_path=CRYPTO_LOAN_REST_API_PROD_URL
        )
        self.crypto_loan_client = CryptoLoan(config_rest_api=configuration)

    # region =======================================================spot
    # 获取最新价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return float(self.spot_exchange.ticker_price(symbol=symbol.upper() + 'USDT')['price'])

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        {'ETH/USDT':
            {'symbol': 'ETH/USDT',
            'timestamp': None,
            'datetime': None,
            'high': None,
            'low': None,
            'bid': 1344.17,
            'bidVolume': 7.07067,
            'ask': 1344.18,
            'askVolume': 33.40117,
            'vwap': None,
            'open': None,
            'close': None,
            'last': None,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': None,
            'info': {'symbol': 'ETHUSDT',
                    'bidPrice': '1344.17000000',
                    'bidQty': '7.07067000',
                    'askPrice': '1344.18000000',
                    'askQty': '33.40117000'}
            }
        }
        """
        try:
            return float(self.spot_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['bidPrice'])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        {'ETH/USDT':
            {'symbol': 'ETH/USDT',
            'timestamp': None,
            'datetime': None,
            'high': None,
            'low': None,
            'bid': 1344.17,
            'bidVolume': 7.07067,
            'ask': 1344.18,
            'askVolume': 33.40117,
            'vwap': None,
            'open': None,
            'close': None,
            'last': None,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': None,
            'info': {'symbol': 'ETHUSDT',
                    'bidPrice': '1344.17000000',
                    'bidQty': '7.07067000',
                    'askPrice': '1344.18000000',
                    'askQty': '33.40117000'}
            }
        }
        """
        try:
            return float(self.spot_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['askPrice'])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        {
          "bid": [
            "61049.98000000",
            "2.69420000"
          ],
          "ask": [
            "61049.99000000",
            "0.21952000"
          ]
        }
        """
        try:
            raw_orderbook = self.spot_exchange.book_ticker(symbol=symbol.upper() + 'USDT')
            return {
                'bid': [raw_orderbook['bidPrice'], raw_orderbook['bidQty']],
                'ask': [raw_orderbook['askPrice'], raw_orderbook['askQty']]
            }
        except:
            print(format_exc())
            return None

    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              61102.74,
              2.49015
            ],
            [
              61102.73,
              0.01893
            ],
            ...
          ],
          "asks": [
            [
              61102.75,
              0.01583
            ],
            [
              61102.79,
              9e-05
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.spot_exchange.depth(symbol=symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self):
        pass

    # 获取现货信息(TODO:暂未统一格式)
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            symbol: "BTCUSDT",
            status: "TRADING",
            baseAsset: "BTC",
            baseAssetPrecision: 8,
            quoteAsset: "USDT",
            quotePrecision: 8,
            quoteAssetPrecision: 8,
            baseCommissionPrecision: 8,
            quoteCommissionPrecision: 8,
            orderTypes: [
                "LIMIT",
                "LIMIT_MAKER",
                "MARKET",
                "STOP_LOSS",
                "STOP_LOSS_LIMIT",
                "TAKE_PROFIT",
                "TAKE_PROFIT_LIMIT"
            ],
            icebergAllowed: True,
            ocoAllowed: True,
            otoAllowed: True,
            quoteOrderQtyMarketAllowed: True,
            allowTrailingStop: True,
            cancelReplaceAllowed: True,
            amendAllowed: True,
            isSpotTradingAllowed: True,
            isMarginTradingAllowed: True,
            filters: [
                {
                filterType: "PRICE_FILTER",
                minPrice: "0.01000000",
                maxPrice: "1000000.********",
                tickSize: "0.01000000"
                },
                {
                filterType: "LOT_SIZE",
                minQty: "0.00001000",
                maxQty: "9000.********",
                stepSize: "0.00001000"
                },
                { filterType: "ICEBERG_PARTS", limit: 10 },
                {
                filterType: "MARKET_LOT_SIZE",
                minQty: "0.********",
                maxQty: "92.59295875",
                stepSize: "0.********"
                },
                {
                filterType: "TRAILING_DELTA",
                minTrailingAboveDelta: 10,
                maxTrailingAboveDelta: 2000,
                minTrailingBelowDelta: 10,
                maxTrailingBelowDelta: 2000
                },
                {
                filterType: "PERCENT_PRICE_BY_SIDE",
                bidMultiplierUp: "5",
                bidMultiplierDown: "0.2",
                askMultiplierUp: "5",
                askMultiplierDown: "0.2",
                avgPriceMins: 5
                },
                {
                filterType: "NOTIONAL",
                minNotional: "5.********",
                applyMinToMarket: True,
                maxNotional: "9000000.********",
                applyMaxToMarket: False,
                avgPriceMins: 5
                },
                { filterType: "MAX_NUM_ORDERS", maxNumOrders: 200 },
                { filterType: "MAX_NUM_ALGO_ORDERS", maxNumAlgoOrders: 5 }
            ],
            permissions: [],
            permissionSets: [
                [
                "SPOT",
                "MARGIN",
                "TRD_GRP_004",
                "TRD_GRP_005",
                "TRD_GRP_006",
                ......
                ]
            ],
            defaultSelfTradePreventionMode: "EXPIRE_MAKER",
            allowedSelfTradePreventionModes: [
                "EXPIRE_TAKER",
                "EXPIRE_MAKER",
                "EXPIRE_BOTH",
                "DECREMENT"
            ]
            }
        """
        try:
            return self.spot_exchange.exchange_info(symbol=symbol.upper() + 'USDT')['symbols'][0]
        except:
            print(format_exc())
            return None
        
    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round((log(1 / float(self.spot_exchange.exchange_info(symbol=symbol.upper() + 'USDT')['symbols'][0]['filters'][0]['tickSize']), 10)), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.spot_exchange.exchange_info(symbol=symbol.upper() + 'USDT')['symbols'][0]['filters'][1]['minQty']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round((log(1 / float(self.spot_exchange.exchange_info(symbol=symbol.upper() + base_symbol.upper())['symbols'][0]['filters'][0]['tickSize']), 10)), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.spot_exchange.exchange_info(symbol=symbol.upper() + base_symbol.upper())['symbols'][0]['filters'][1]['minQty']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            return float(self.spot_exchange.exchange_info(symbol=symbol.upper() + 'USDT')['symbols'][0]['filters'][1]['minQty'])
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
        :return:
        """
        try:
            symbol_list = []
            _info = self.spot_exchange.exchange_info()['symbols']
            base_symbol_len = len(base_symbol)
            symbol_list.extend([i['symbol'].lower() for i in _info if all(
                [i['status'] == 'TRADING' and i['symbol'].lower()[-base_symbol_len:] == base_symbol])])
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            info = self.spot_exchange.account()['balances']
            for i in info:
                if i['asset'] == symbol.upper():
                    return float(i['free'])
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
            {
              "btc": "0.********",
              "bnb": "51.********",
              "usdt": "1498.********",
              "usdc": "0.********",
              "doge": "6925.********",
              "wif": "96802.********"
            }
        """
        try:
            balance = self.spot_exchange.account()['balances']
            if balance:
                return {i['asset'].lower(): float(i['free']) for i in balance if float(i['free']) != 0}
            else:
                return None
        except Exception as e:
            print("BINANCE查询币种余额出错：", e)
            if 'binance 429 Too Many Requests' or 'binance 418' in str(e):
                print('触发币安binance 429 Too Many Requests报警，等待5秒后继续运行！')
                sleep(5)
            return None

    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
        [
            {
                symbol: "BTCUSDT",
                orderId: ***********,
                orderListId: -1,
                clientOrderId: "web_dc8ac379bee541b0997e8ec6985fb881",
                price: "100000.********",
                origQty: "0.********",
                executedQty: "0.********",
                cummulativeQuoteQty: "0.********",
                status: "NEW",
                timeInForce: "GTC",
                type: "LIMIT",
                side: "BUY",
                stopPrice: "0.********",
                icebergQty: "0.********",
                time: 1748398028258,
                updateTime: 1748398028258,
                isWorking: True,
                workingTime: 1748398028258,
                origQuoteOrderQty: "0.********",
                selfTradePreventionMode: "EXPIRE_MAKER"
            }
            ]
        """
        try:
            open_orders = self.spot_exchange.get_open_orders(symbol=symbol.upper() + 'USDT')
            return [{
                'order_id': order['orderId'],
                'symbol': symbol,
                'direction': order['side'].lower(),
                'amount': order['origQty'],
                'price': order['price'],
                'order_type': order['type'].lower(),
                'average_price': '',
                'remain_amount': float(order['origQty']) - float(order['executedQty']),
            } for order in open_orders]
        except:
            print(format_exc())
            return None

    # 下单
    def place_spot_order(self, symbol, direction, amount=None, price=None, order_type='limit', quoteorderqty=None):
        """"
        return:
        {
            symbol: "BTCUSDT",
            orderId: 43834559716,
            orderListId: -1,
            clientOrderId: "wZrNNyU4OZU9B8WNTki3A0",
            transactTime: 1748399318885,
            price: "100000.********",
            origQty: "0.********",
            executedQty: "0.********",
            origQuoteOrderQty: "0.********",
            cummulativeQuoteQty: "0.********",
            status: "NEW",
            timeInForce: "GTC",
            type: "LIMIT",
            side: "BUY",
            workingTime: 1748399318885,
            fills: [],
            selfTradePreventionMode: "EXPIRE_MAKER"
            }
        """
        try:
            if order_type == 'limit':
                order_info = self.spot_exchange.new_order(
                    symbol=symbol.upper() + 'USDT', side=direction, type='LIMIT', quantity=amount, price=price, 
                    timeInForce='GTC', quoteOrderQty=quoteorderqty)
            elif order_type == 'market':
                if quoteorderqty:
                    order_info = self.spot_exchange.new_order(
                        symbol=symbol.upper() + 'USDT', side=direction, type='MARKET', quoteOrderQty=quoteorderqty)
                else:
                    order_info = self.spot_exchange.new_order(
                        symbol=symbol.upper() + 'USDT', side=direction, type='MARKET', quantity=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                # 在市价单params['quoteOrderQty']情况下，amount表示quoteOrderQty，这里amount显示的数据是quoteOrderQty(usdt)
                'amount': amount if order_type == 'limit' else order_info['origQty'],
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 下单
    def place_spot_order2(self, symbol, base_symbol, direction, amount=None, price=None, order_type='limit'):
        try:
            if order_type == 'limit':
                order_info = self.spot_exchange.new_order(
                    symbol=(symbol + base_symbol).upper(), side=direction, type='LIMIT', quantity=amount, price=price, 
                    timeInForce='GTC')
            elif order_type == 'market':
                order_info = self.spot_exchange.new_order(
                    symbol=(symbol + base_symbol).upper(), side=direction, type='MARKET', quantity=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'base_symbol': base_symbol,
                'direction': direction,
                'amount': amount if order_type == 'limit' else order_info['origQty'],
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None
        
    # 获取订单信息
    def get_spot_order_info(self, symbol, order_id):
        """
        获取订单信息
        [
            {
                symbol: "ETHUSDT",
                id: "1587048248",
                orderId: "20232077784",
                orderListId: "-1",
                price: "2545.********",
                qty: "0.00200000",
                quoteQty: "5.09000000",
                commission: "0.00000672",
                commissionAsset: "BNB",
                time: "1726891238903",
                isBuyer: True,
                isMaker: False,
                isBestMatch: True
            },
            {
                symbol: "ETHUSDT",
                id: "1587048249",
                orderId: "20232077784",
                orderListId: "-1",
                price: "2545.********",
                qty: "0.16590000",
                quoteQty: "422.21550000",
                commission: "0.00055745",
                commissionAsset: "BNB",
                time: "1726891238903",
                isBuyer: True,
                isMaker: False,
                isBestMatch: True
            },
            ......
        ]
        """
        try:
            fee = 0
            fill_info = self.spot_exchange.my_trades(symbol=symbol.upper() + 'USDT', orderId=order_id)
            for i in fill_info:
                fee += float(i['commission'])
            order_info = self.spot_exchange.get_order(symbol=symbol.upper() + 'USDT', orderId=order_id)
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['type'].lower(),
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'average_price': float(order_info['cummulativeQuoteQty']) / float(order_info['origQty']),
                'remain_amount': float(order_info['origQty']) - float(order_info['executedQty']),
                'fee': fee,
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            fee = 0
            fill_info = self.spot_exchange.my_trades(symbol=(symbol + base_symbol).upper(), orderId=order_id)
            for i in fill_info:
                fee += float(i['commission'])
            order_info = self.spot_exchange.get_order(
                symbol=(symbol + base_symbol).upper(), orderId=order_id)
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['type'].lower(),
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'average_price': float(order_info['cummulativeQuoteQty']) / float(order_info['origQty']),
                'remain_amount': float(order_info['origQty']) - float(order_info['executedQty']),
                'fee': fee,
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_spot_order(self, symbol, order_id):
        """
        {
            symbol: "BTCUSDT",
            origClientOrderId: "web_839aad916945436f8e40e717e9a28755",
            orderId: 43833866358,
            orderListId: -1,
            clientOrderId: "gS6RCCiQCANzoqTa4IgAOe",
            transactTime: 1748398450552,
            price: "100000.********",
            origQty: "0.********",
            executedQty: "0.********",
            origQuoteOrderQty: "0.********",
            cummulativeQuoteQty: "0.********",
            status: "CANCELED",
            timeInForce: "GTC",
            type: "LIMIT",
            side: "BUY",
            selfTradePreventionMode: "EXPIRE_MAKER"
            }
        """
        try:
            order_info = self.spot_exchange.cancel_order(symbol=symbol.upper() + 'USDT', orderId=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['status'] == 'CANCELED' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # 资产划转(TODO:母子账户互转)
    def transfer_asset(self, amount, symbol='USDT', from_account='', to_account='', from_account_type='spot', to_account_type='usdt-swap'):
        """
        通用资产划转函数,支持母子账户之间以及不同账户类型之间的划转
        
        :param from_account: 转出账户,可以是'spot','swap','future',或者子账户邮箱   # TODO: 母子账户互转需要修改，本函数不支持
        :param to_account: 转入账户,可以是'spot','swap','future',或者子账户邮箱   # TODO: 母子账户互转需要修改，本函数不支持
        :param amount: 划转数量
        :param symbol: 划转的币种,默认为'USDT'
        :param from_account_type: 转出账户类型,可选"SPOT","USDT_FUTURE","COIN_FUTURE","MARGIN","ISOLATED_MARGIN"
        :param to_account_type: 转入账户类型,可选"SPOT","USDT_FUTURE","COIN_FUTURE","MARGIN","ISOLATED_MARGIN"
        :return: 划转结果
        """
        # 判断是否为母子账户之间的划转
        _type = {
            'spot': 'MAIN',
            'usdt-swap': 'UMFUTURE',
            'coin-swap': 'CMFUTURE',
            'margin': 'MARGIN', # 杠杆全仓钱包
            'isolated-margin': 'ISOLATED_MARGIN', # 杠杆逐仓钱包
            'funding': 'FUNDING', # 资金账户
            'option': 'OPTION', # 期权账户
            'portfolio': 'PORTFOLIO_MARGIN', # 统一账户
        }
        info = self.spot_exchange.user_universal_transfer(type=_type[from_account_type] + '_' + _type[to_account_type], asset=symbol.upper(), amount=float(amount))
        if info['tranId']:
            return {'symbol': symbol, 'amount': amount, 'from_account_type': from_account_type, 'to_account_type': to_account_type}
        else:
            return None
        
    # region ==========================================================小额报价的meme coin
    # 获取最新价格
    def get_spot_last_price_kcoin(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return float(self.spot_exchange.ticker_price(symbol=symbol.upper() + 'USDT')['price']) * 1000

    # 获取spot盘口买1价
    def get_spot_buy1_kcoin(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        {'ETH/USDT':
            {'symbol': 'ETH/USDT',
            'timestamp': None,
            'datetime': None,
            'high': None,
            'low': None,
            'bid': 1344.17,
            'bidVolume': 7.07067,
            'ask': 1344.18,
            'askVolume': 33.40117,
            'vwap': None,
            'open': None,
            'close': None,
            'last': None,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': None,
            'info': {'symbol': 'ETHUSDT',
                    'bidPrice': '1344.17000000',
                    'bidQty': '7.07067000',
                    'askPrice': '1344.18000000',
                    'askQty': '33.40117000'}
            }
        }
        """
        try:
            return float(self.spot_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['bidPrice']) * 1000
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1_kcoin(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        {'ETH/USDT':
            {'symbol': 'ETH/USDT',
            'timestamp': None,
            'datetime': None,
            'high': None,
            'low': None,
            'bid': 1344.17,
            'bidVolume': 7.07067,
            'ask': 1344.18,
            'askVolume': 33.40117,
            'vwap': None,
            'open': None,
            'close': None,
            'last': None,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': None,
            'info': {'symbol': 'ETHUSDT',
                    'bidPrice': '1344.17000000',
                    'bidQty': '7.07067000',
                    'askPrice': '1344.18000000',
                    'askQty': '33.40117000'}
            }
        }
        """
        try:
            return float(self.spot_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['askPrice']) * 1000
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook_kcoin(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        {
          "bid": [
            "61049.98000000",
            "2.69420000"
          ],
          "ask": [
            "61049.99000000",
            "0.21952000"
          ]
        }
        """
        try:
            raw_orderbook = self.spot_exchange.book_ticker(symbol= '1000' + symbol.upper() + 'USDT')
            return {
                'bid': [float(raw_orderbook['bidPrice']), float(raw_orderbook['bidQty'])],
                'ask': [float(raw_orderbook['askPrice']), float(raw_orderbook['askQty'])]
            }
        except:
            print(format_exc())
            return None

    def get_spot_orderbook_kcoin(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              61102.74,
              2.49015
            ],
            [
              61102.73,
              0.01893
            ],
            ...
          ],
          "asks": [
            [
              61102.75,
              0.01583
            ],
            [
              61102.79,
              9e-05
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.spot_exchange.depth(symbol='1000' + symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None
        
    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return self.spot_exchange.exchange_info(symbol='1000' + symbol.upper() + 'USDT')['symbols'][0]
        except:
            print(format_exc())
            return None
    
    # 获取下单价格精度
    def get_spot_order_price_tick_size_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round((log(1 / float(self.spot_exchange.exchange_info(symbol='1000' + symbol.upper() + 'USDT')['symbols'][0]['filters'][0]['tickSize']), 10)), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.spot_exchange.exchange_info(symbol='1000' + symbol.upper() + 'USDT')['symbols'][0]['filters'][1]['minQty']), 10), 0))
        except:
            print(format_exc())
            return None
    
    # TODO:添加meme coin下单等函数
    # 目前binance的现货盘口只有cat是1000cat，其他的币种都是正常币种

    # endregion =======================================================小额报价的meme coin

    # endregion =======================================================spot

    # TODO 交割合约
    # region =======================================================future
    # 获取合约当期instrument_id
    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:
        """
        instrument_id = self.exchange.get_products()
        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['alias'] == contract_type and i['quote_currency'] == margin_type2:
                    return i['instrument_id']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['quote_currency'] == margin_type2:
                    instrument_id_list.append(i['instrument_id'])
            return instrument_id_list

    # 获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])

    # 获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])

    # # 获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion =======================================================future


    # region =======================================================usdt swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            return float(self.swap_exchange.ticker_price(symbol=symbol.upper() + 'USDT')['price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['bidPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.book_ticker(symbol=symbol.upper() + 'USDT')['askPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60896.40', '4.079'], 'ask': ['60896.50', '5.771']}
        """
        try:
            raw_orderbook = self.swap_exchange.book_ticker(symbol=symbol.upper() + 'USDT')
            return {
                'bid': [raw_orderbook['bidPrice'], raw_orderbook['bidQty']],
                'ask': [raw_orderbook['askPrice'], raw_orderbook['askQty']]
            }
        except:
            print(format_exc())
            return None
        
    # 获取swap orderbook
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "61069.90",
              "2.459"
            ],
            [
              "61069.70",
              "0.270"
            ],
            ...
          ],
          "asks": [
            [
              "61070.00",
              "4.226"
            ],
            [
              "61070.30",
              "0.035"
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.swap_exchange.depth(symbol=symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.swap_exchange.mark_price(symbol=symbol.upper() + 'USDT')['lastFundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
            {
              "symbol": "btc",
              "funding_rate": [
                "0.00004846",
                "-0.00003942",
                "-0.00001379",
                "-0.00001949",
                "0.00004049"
              ],
              "funding_time": [
                "1723219200000",
                "1723248000000",
                "1723276800000",
                "1723305600000",
                "1723334400000"
              ]
            }
        """
        try:
            funding_rates = self.swap_exchange.funding_rate(
                symbol=symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
            u本位：
            {'symbol': 'BNBUSDT',
            'sumOpenInterest': '953893.97000000',   持仓的bnb数量
            'sumOpenInterestValue': '350101980.44528000', 持仓的bnb价值(usdt)
            'timestamp': 1617795600000
            }
        """
        try:
            return float(self.swap_exchange.open_interest_hist(symbol=symbol.upper() + 'USDT', period='5m')[-1]['sumOpenInterestValue'])
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
            {
                "symbol": "LOOMUSDT",
                "pair": "LOOMUSDT",
                "contractType": "PERPETUAL",
                "deliveryDate": "4133404800000",
                "onboardDate": "1697034600000",
                "status": "TRADING",
                "maintMarginPercent": "2.5000",
                "requiredMarginPercent": "5.0000",
                "baseAsset": "LOOM",
                "quoteAsset": "USDT",
                "marginAsset": "USDT",
                "pricePrecision": "7",
                "quantityPrecision": "0",
                "baseAssetPrecision": "8",
                "quotePrecision": "8",
                "underlyingType": "COIN",
                "underlyingSubType": [],
                "settlePlan": "0",
                "triggerProtect": "0.1500",
                "liquidationFee": "0.025000",
                "marketTakeBound": "0.15",
                "maxMoveOrderLimit": "10000",
                "filters": [
                    {
                        "filterType": "PRICE_FILTER",
                        "maxPrice": "200",
                        "tickSize": "0.0001000",
                        "minPrice": "0.0001000"
                    },
                    {
                        "filterType": "LOT_SIZE",
                        "maxQty": "10000000",
                        "stepSize": "1",
                        "minQty": "1"
                    },
                    {
                        "filterType": "MARKET_LOT_SIZE",
                        "maxQty": "800000",
                        "minQty": "1",
                        "stepSize": "1"
                    },
                    {
                        "filterType": "MAX_NUM_ORDERS",
                        "limit": "200"
                    },
                    {
                        "limit": "10",
                        "filterType": "MAX_NUM_ALGO_ORDERS"
                    },
                    {
                        "filterType": "MIN_NOTIONAL",
                        "notional": "5"
                    },
                    {
                        "multiplierDecimal": "4",
                        "multiplierUp": "1.1500",
                        "multiplierDown": "0.8500",
                        "filterType": "PERCENT_PRICE"
                    }
                ],
                "orderTypes": [
                    "LIMIT",
                    "MARKET",
                    "STOP",
                    "STOP_MARKET",
                    "TAKE_PROFIT",
                    "TAKE_PROFIT_MARKET",
                    "TRAILING_STOP_MARKET"
                ],
                "timeInForce": [
                    "GTC",
                    "IOC",
                    "FOK",
                    "GTX",
                    "GTD"
                ]
            }
        """
        try:
            _info = self.swap_exchange.exchange_info()['symbols']
            for i in _info:
                if i['symbol'] == symbol.upper() + 'USDT':
                    return i
        except:
            print(format_exc())
            return None
        
    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters'][0]['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters'][1]['stepSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.get_swap_instruments_info(symbol)['filters']
            _min_amount = float(_info[1]['minQty'])
            _min_nominal = float(_info[5]['notional'])
            _price = self.get_swap_latest_price(symbol)
            _amount_precision = int(round(log(1 / float(_info[1]['stepSize']), 10), 0))
            _min_amount2 = ceil(_min_nominal / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            return max(_min_amount, _min_amount2)
        except:
            print(format_exc())
            return None
        
    # 获取永续合约币对
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
        :return:
        """
        try:
            symbol_list = []
            base_symbol_len = len(base_symbol)
            _info = self.swap_exchange.exchange_info()['symbols']
            symbol_list.extend([i['symbol'].lower() for i in _info if all(
                [i['status'] == 'TRADING' and i['symbol'].lower()[-base_symbol_len:] == base_symbol])])
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        try:
            account_info = self.swap_exchange.balance()
            for i in account_info:
                if i['asset'] == symbol.upper():
                    return float(i['balance'])
        except:
            print(format_exc())
            return None

    # 获取swap账户信息(TODO：暂未统一到其他交易所)
    def get_swap_account_info(self):
        """
        {
            totalInitialMargin: "817088.********",
            totalMaintMargin: "107888.********",
            totalWalletBalance: "1647584.********",
            totalUnrealizedProfit: "-212310.********",
            totalMarginBalance: "1435274.********",
            totalPositionInitialMargin: "817088.********",
            totalOpenOrderInitialMargin: "0.********",
            totalCrossWalletBalance: "1647584.********",
            totalCrossUnPnl: "-212310.********",
            availableBalance: "617428.********",
            maxWithdrawAmount: "617428.********",
            assets: [
                {
                asset: "FDUSD",
                walletBalance: "0.********",
                unrealizedProfit: "0.********",
                marginBalance: "0.********",
                maintMargin: "0.********",
                initialMargin: "0.********",
                positionInitialMargin: "0.********",
                openOrderInitialMargin: "0.********",
                crossWalletBalance: "0.********",
                crossUnPnl: "0.********",
                availableBalance: "611439.14806361",
                maxWithdrawAmount: "0.********",
                updateTime: 0
                },
                {
                asset: "LDUSDT",
                walletBalance: "0.********",
                unrealizedProfit: "0.********",
                marginBalance: "0.********",
                maintMargin: "0.********",
                initialMargin: "0.********",
                positionInitialMargin: "0.********",
                openOrderInitialMargin: "0.********",
                crossWalletBalance: "0.********",
                crossUnPnl: "0.********",
                availableBalance: "557364.********",
                maxWithdrawAmount: "0.********",
                updateTime: 0
                },
                ......
                ]
            }
        """
        try:
            return self.swap_exchange.account()
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
        {
            totalInitialMargin: "13.********",
            totalMaintMargin: "1.********",
            totalWalletBalance: "1245.********",
            totalUnrealizedProfit: "2.********",
            totalMarginBalance: "1247.********",
            totalPositionInitialMargin: "13.********",
            totalOpenOrderInitialMargin: "0.********",
            totalCrossWalletBalance: "1245.********",
            totalCrossUnPnl: "2.********",
            availableBalance: "1233.********",
            maxWithdrawAmount: "1233.********",
            assets: [
                {
                asset: "FDUSD",
                walletBalance: "0.********",
                unrealizedProfit: "0.********",
                marginBalance: "0.********",
                maintMargin: "0.********",
                initialMargin: "0.********",
                positionInitialMargin: "0.********",
                openOrderInitialMargin: "0.********",
                crossWalletBalance: "0.********",
                crossUnPnl: "0.********",
                availableBalance: "1218.60645025",
                maxWithdrawAmount: "0.********",
                updateTime: "0"
                },
                {
                asset: "BTC",
                walletBalance: "0.********",
                unrealizedProfit: "0.********",
                marginBalance: "0.********",
                maintMargin: "0.********",
                initialMargin: "0.********",
                positionInitialMargin: "0.********",
                openOrderInitialMargin: "0.********",
                crossWalletBalance: "0.********",
                crossUnPnl: "0.********",
                availableBalance: "0.********",
                maxWithdrawAmount: "0.********",
                updateTime: "0"
                },
            ......]
        }
        """
        try:
            _info = self.swap_exchange.account()
            _margin_rate = float(_info['totalMaintMargin']) / float(_info['totalMarginBalance'])
            return _margin_rate
        except:
            print(format_exc())
            return None
        
    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
              {
                "symbol": "BTCUSDT",
                "positionSide": "BOTH",
                "positionAmt": "-0.005",
                "entryPrice": "60183.7",
                "breakEvenPrice": "60159.62652",
                "markPrice": "60579.********",
                "unRealizedProfit": "-1.********",
                "liquidationPrice": "259122.********",
                "isolatedMargin": "0",
                "notional": "-302.********",
                "marginAsset": "USDT",
                "isolatedWallet": "0",
                "initialMargin": "151.44900000",
                "maintMargin": "1.21159200",
                "positionInitialMargin": "151.44900000",
                "openOrderInitialMargin": "0",
                "adl": "1",
                "bidNotional": "0",
                "askNotional": "0",
                "updateTime": "1723209932783"
              }
        """
        try:
            if symbol:
                positions = self.swap_exchange.get_position_risk(symbol=symbol.upper() + 'USDT')
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': abs(float(i['positionAmt'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
            else:
                positions = self.swap_exchange.get_position_risk()
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': abs(float(i['positionAmt'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
            [
              {
                "orderId": "************",
                "symbol": "BTCUSDT",
                "status": "NEW",
                "clientOrderId": "web_PmaN8HBLN50nrWFJwNBn",
                "price": "60000",
                "avgPrice": "0",
                "origQty": "0.002",
                "executedQty": "0",
                "cumQuote": "0.00000",
                "timeInForce": "GTC",
                "type": "LIMIT",
                "reduceOnly": false,
                "closePosition": false,
                "side": "SELL",
                "positionSide": "BOTH",
                "stopPrice": "0",
                "workingType": "CONTRACT_PRICE",
                "priceProtect": false,
                "origType": "LIMIT",
                "priceMatch": "NONE",
                "selfTradePreventionMode": "NONE",
                "goodTillDate": "0",
                "time": "1723214436305",
                "updateTime": "1723214436305"
              }
            ]
        """
        try:
            if symbol:
                open_orders = self.swap_exchange.get_orders(symbol=symbol.upper() + 'USDT')
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': symbol,
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
            else:
                # 不带symbol的权重为40 请小心使用不带symbol参数的调用
                open_orders = self.swap_exchange.get_orders()
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.swap_exchange.get_orders(symbol=symbol.upper() + 'USDT')[0]['orderId']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.swap_exchange.new_order(symbol=symbol.upper() + 'USDT', side=direction.upper(), type='LIMIT',
                                                                            quantity=amount, price=price, timeInForce='GTC', reduceOnly='true')
                else:
                    order_info = self.swap_exchange.new_order(symbol=symbol.upper() + 'USDT', side=direction.upper(), type='LIMIT',
                                                                            quantity=amount, price=price, timeInForce='GTC')
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.swap_exchange.new_order(symbol=symbol.upper() + 'USDT', side=direction.upper(), type='MARKET',
                                                                            quantity=amount, reduceOnly='true')
                else:
                    order_info = self.swap_exchange.new_order(symbol=symbol.upper() + 'USDT', side=direction.upper(), type='MARKET',
                                                                            quantity=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_swap_order_info(self, symbol, order_id):
        """
        [
            {
                symbol: "TIAUSDT",
                id: "23134821",
                orderId: "*********",
                orderListId: "-1",
                price: "11.95090000",
                qty: "14.********",
                quoteQty: "174.48314000",
                commission: "0.00042597",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            },
            {
                symbol: "TIAUSDT",
                id: "23134822",
                orderId: "*********",
                orderListId: "-1",
                price: "11.95050000",
                qty: "26.20000000",
                quoteQty: "313.10310000",
                commission: "0.00076440",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            },
            {
                symbol: "TIAUSDT",
                id: "23134823",
                orderId: "*********",
                orderListId: "-1",
                price: "11.********",
                qty: "19.********",
                quoteQty: "227.********",
                commission: "0.********",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            }
            ]
        """
        try:
            fee = 0
            fill_info = self.swap_exchange.get_account_trades(symbol=symbol.upper() + 'USDT', orderId=order_id)
            for i in fill_info:
                fee += float(i['commission'])
            order_info = self.swap_exchange.query_order(symbol=symbol.upper() + 'USDT', orderId=order_id)
            # TODO：考虑添加status字段
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['type'].lower(),
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': float(order_info['origQty']) - float(order_info['executedQty']),
                'fee': fee,
            }
        except:
            print(format_exc())
            return None

    # 撤单
    def cancel_swap_order(self, symbol, order_id):
        try:
            cancel_order_info = self.swap_exchange.cancel_order(symbol=symbol.upper() + 'USDT', orderId=order_id)
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['status'] == 'CANCELED' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # 设置杠杆(默认3倍)(TODO:暂未统一到其他交易所)
    def change_swap_leverage(self, symbol, leverage=3):
        """
        设置杠杆
        @param symbol:  btc
        @param leverage: 杠杆倍数
        @return:
        {'symbol': 'MOVEUSDT', 'leverage': '10', 'maxNotionalValue': '1000000'}
        {'symbol': 'MOVEUSDT', 'leverage': '5', 'maxNotionalValue': '2000000'}
        """
        try:
            return self.swap_exchange.change_leverage(symbol=symbol.upper() + 'USDT', leverage=leverage)
        except:
            print(format_exc())
            return None

    # 获取用户资金费率历史
    def get_funding_fee(self, symbol, margin_type='usdt', start_time=None, end_time=None, days=None):
        pass

    # TODO:获取下单记录（注意：获取到的是下单明细）
    def fetch_my_swap_orders(self, symbol, date=1, direction='buy', margin_type='usdt'):
        """

        :param symbol:
        :param margin_type:
        :return:
        """
        days_ago = int(time() * 1000) - date * 24 * 60 * 60 * 1000
        if margin_type == 'coin':
            pass
        elif margin_type == 'usdt':
            info = self.exchange.fapiPrivateGetUserTrades(params={'symbol': symbol.upper() + 'USDT', 'limit': 1000})
        df = pd.DataFrame(info)
        if int(info[0]['time']) > days_ago:
            while True:
                end_time = int(info[0]['time'])
                info = self.exchange.fapiPrivateGetUserTrades(params={'symbol': symbol.upper() + 'USDT', 'limit': 1000, 'endTime': end_time})
                if info[0]['time'] == df.iloc[-1]['time']:
                    break
                df = pd.concat([df, pd.DataFrame(info)], axis=0)
                if int(info[0]['time']) < days_ago:
                    break
        df['datetime'] = pd.to_datetime(pd.to_numeric(df['time']), unit='ms') + timedelta(hours=8)
        df = df[df['side'] == direction.upper()]
        df.sort_values(by='datetime', ascending=False, inplace=True)
        df.drop_duplicates(subset=['id'], keep='first', inplace=True)
        # 截取最近两天时间的数据
        df = df[df['datetime'] > pd.to_datetime(days_ago, unit='ms')]
        df.reset_index(drop=True, inplace=True)
        return df
    
    # region =======================================================小额报价的meme coin
        # 获取最新价格
    def get_swap_latest_price_kcoin(self, symbol):
        try:
            return float(self.swap_exchange.ticker_price(symbol='1000' + symbol.upper() + 'USDT')['price'])
        except:
            print(format_exc())
            return None
        
    # 获取swap盘口买1价
    def get_swap_buy1_kcoin(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.book_ticker(symbol='1000' + symbol.upper() + 'USDT')['bidPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1_kcoin(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.book_ticker(symbol='1000' + symbol.upper() + 'USDT')['askPrice'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60896.40', '4.079'], 'ask': ['60896.50', '5.771']}
        """
        try:
            raw_orderbook = self.swap_exchange.book_ticker(symbol='1000' + symbol.upper() + 'USDT')
            return {
                'bid': [raw_orderbook['bidPrice'], raw_orderbook['bidQty']],
                'ask': [raw_orderbook['askPrice'], raw_orderbook['askQty']]
            }
        except:
            print(format_exc())
            return None

    # 获取swap orderbook
    def get_swap_orderbook_kcoin(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "61069.90",
              "2.459"
            ],
            [
              "61069.70",
              "0.270"
            ],
            ...
          ],
          "asks": [
            [
              "61070.00",
              "4.226"
            ],
            [
              "61070.30",
              "0.035"
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.swap_exchange.depth(symbol='1000' + symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': '1000' + symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.swap_exchange.mark_price(symbol='1000' + symbol.upper() + 'USDT')['lastFundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate_kcoin(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
            {
              "symbol": "btc",
              "funding_rate": [
                "0.00004846",
                "-0.00003942",
                "-0.00001379",
                "-0.00001949",
                "0.00004049"
              ],
              "funding_time": [
                "1723219200000",
                "1723248000000",
                "1723276800000",
                "1723305600000",
                "1723334400000"
              ]
            }
        """
        try:
            funding_rates = self.swap_exchange.funding_rate(symbol='1000' + symbol.upper() + 'USDT', limit=limit)
            return {
                'symbol': '1000' + symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest_kcoin(self, symbol):
        """
        :param symbol:
            u本位：
            {'symbol': 'BNBUSDT',
            'sumOpenInterest': '953893.97000000',   持仓的bnb数量
            'sumOpenInterestValue': '350101980.44528000', 持仓的bnb价值(usdt)
            'timestamp': 1617795600000
            }
        """
        try:
            return float(self.swap_exchange.open_interest_hist(symbol='1000' + symbol.upper() + 'USDT', contractType='PERPETUAL', period='5m')[-1]['sumOpenInterestValue'])
        except:
            print(format_exc())
            return None

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        {
            symbol: "1000PEPEUSDT",
            pair: "1000PEPEUSDT",
            contractType: "PERPETUAL",
            deliveryDate: "4133404800000",
            onboardDate: "1683244800000",
            status: "TRADING",
            maintMarginPercent: "2.5000",
            requiredMarginPercent: "5.0000",
            baseAsset: "1000PEPE",
            quoteAsset: "USDT",
            marginAsset: "USDT",
            pricePrecision: "7",
            quantityPrecision: "0",
            baseAssetPrecision: "8",
            quotePrecision: "8",
            underlyingType: "COIN",
            underlyingSubType: ["Meme"],
            triggerProtect: "0.1500",
            liquidationFee: "0.015000",
            marketTakeBound: "0.15",
            maxMoveOrderLimit: "10000",
            filters: [
                {
                tickSize: "0.0000001",
                filterType: "PRICE_FILTER",
                maxPrice: "200",
                minPrice: "0.0000001"
                },
                { stepSize: "1", maxQty: "8********", filterType: "LOT_SIZE", minQty: "1" },
                {
                maxQty: "1********",
                minQty: "1",
                stepSize: "1",
                filterType: "MARKET_LOT_SIZE"
                },
                { filterType: "MAX_NUM_ORDERS", limit: "200" },
                { filterType: "MAX_NUM_ALGO_ORDERS", limit: "10" },
                { notional: "5", filterType: "MIN_NOTIONAL" },
                {
                multiplierUp: "1.1500",
                multiplierDecimal: "4",
                filterType: "PERCENT_PRICE",
                multiplierDown: "0.8500"
                }
            ],
            orderTypes: [
                "LIMIT",
                "MARKET",
                "STOP",
                "STOP_MARKET",
                "TAKE_PROFIT",
                "TAKE_PROFIT_MARKET",
                "TRAILING_STOP_MARKET"
            ],
            timeInForce: ["GTC", "IOC", "FOK", "GTX", "GTD"]
            }
        """
        try:
            _info = self.swap_exchange.exchange_info()['symbols']
            for i in _info:
                if i['symbol'] == '1000' + symbol.upper() + 'USDT':
                    return i
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info_kcoin(symbol)['filters'][0]['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size_kcoin(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info_kcoin(symbol)['filters'][1]['stepSize']), 10), 0))
        except:
            print(format_exc())
            return None
    
    # 获取swap账户持仓信息
    def get_swap_position_kcoin(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
              {
                "symbol": "BTCUSDT",
                "positionSide": "BOTH",
                "positionAmt": "-0.005",
                "entryPrice": "60183.7",
                "breakEvenPrice": "60159.62652",
                "markPrice": "60579.********",
                "unRealizedProfit": "-1.********",
                "liquidationPrice": "259122.********",
                "isolatedMargin": "0",
                "notional": "-302.********",
                "marginAsset": "USDT",
                "isolatedWallet": "0",
                "initialMargin": "151.44900000",
                "maintMargin": "1.21159200",
                "positionInitialMargin": "151.44900000",
                "openOrderInitialMargin": "0",
                "adl": "1",
                "bidNotional": "0",
                "askNotional": "0",
                "updateTime": "1723209932783"
              }
        """
        try:
            if symbol:
                positions = self.swap_exchange.get_position_risk(symbol='1000' + symbol.upper() + 'USDT')
                return [
                    {
                        'symbol': '1000' + symbol,
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': abs(float(i['positionAmt'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
            else:
                positions = self.swap_exchange.get_position_risk()
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                        'amount': abs(float(i['positionAmt'])),
                        'price': i['entryPrice'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None
        
    # 获取swap挂单信息
    def get_swap_open_order_kcoin(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
            [
              {
                "orderId": "************",
                "symbol": "BTCUSDT",
                "status": "NEW",
                "clientOrderId": "web_PmaN8HBLN50nrWFJwNBn",
                "price": "60000",
                "avgPrice": "0",
                "origQty": "0.002",
                "executedQty": "0",
                "cumQuote": "0.00000",
                "timeInForce": "GTC",
                "type": "LIMIT",
                "reduceOnly": false,
                "closePosition": false,
                "side": "SELL",
                "positionSide": "BOTH",
                "stopPrice": "0",
                "workingType": "CONTRACT_PRICE",
                "priceProtect": false,
                "origType": "LIMIT",
                "priceMatch": "NONE",
                "selfTradePreventionMode": "NONE",
                "goodTillDate": "0",
                "time": "1723214436305",
                "updateTime": "1723214436305"
              }
            ]
        """
        try:
            if symbol:
                open_orders = self.swap_exchange.get_orders(symbol='1000' + symbol.upper() + 'USDT')
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': '1000' + symbol,
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
            else:
                # 不带symbol的权重为40 请小心使用不带symbol参数的调用
                open_orders = self.swap_exchange.get_orders()
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None
        
    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id_kcoin(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.swap_exchange.get_orders(symbol='1000' + symbol.upper() + 'USDT')[0]['orderId']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order_kcoin(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.swap_exchange.new_order(symbol='1000' + symbol.upper() + 'USDT', side=direction.upper(), type='LIMIT',
                                                              quantity=amount, price=price, timeInForce='GTC', reduceOnly='true')
                else:
                    order_info = self.swap_exchange.new_order(symbol='1000' + symbol.upper() + 'USDT', side=direction.upper(), type='LIMIT',
                                                              quantity=amount, price=price, timeInForce='GTC')
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.swap_exchange.new_order(symbol='1000' + symbol.upper() + 'USDT', side=direction.upper(), type='MARKET',
                                                              quantity=amount, reduceOnly='true')
                else:
                    order_info = self.swap_exchange.new_order(symbol='1000' + symbol.upper() + 'USDT', side=direction.upper(), type='MARKET',
                                                              quantity=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': '1000' + symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None
        
    # 获取订单信息
    def get_swap_order_info_kcoin(self, symbol, order_id):
        """
        [
            {
                symbol: "TIAUSDT",
                id: "23134821",
                orderId: "*********",
                orderListId: "-1",
                price: "11.95090000",
                qty: "14.********",
                quoteQty: "174.48314000",
                commission: "0.00042597",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            },
            {
                symbol: "TIAUSDT",
                id: "23134822",
                orderId: "*********",
                orderListId: "-1",
                price: "11.95050000",
                qty: "26.20000000",
                quoteQty: "313.10310000",
                commission: "0.00076440",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            },
            {
                symbol: "TIAUSDT",
                id: "23134823",
                orderId: "*********",
                orderListId: "-1",
                price: "11.********",
                qty: "19.********",
                quoteQty: "227.********",
                commission: "0.********",
                commissionAsset: "BNB",
                time: "*************",
                isBuyer: False,
                isMaker: False,
                isBestMatch: True
            }
            ]
        """
        try:
            fee = 0
            fill_info = self.swap_exchange.get_account_trades(symbol='1000' + symbol.upper() + 'USDT', orderId=order_id)
            for i in fill_info:
                fee += float(i['commission'])
            order_info = self.swap_exchange.query_order(symbol='1000' + symbol.upper() + 'USDT', orderId=order_id)
            # TODO：考虑添加status字段
            return {
                'exchange': self.exchange_name,
                'order_id': order_id,
                'symbol': '1000' + symbol,
                'direction': order_info['side'].lower(),
                'order_type': order_info['type'].lower(),
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'average_price': order_info['avgPrice'],
                'remain_amount': float(order_info['origQty']) - float(order_info['executedQty']),
                'fee': fee,
            }
        except:
            print(format_exc())
            return None

    # 撤单
    def cancel_swap_order_kcoin(self, symbol, order_id):
        try:
            cancel_order_info = self.swap_exchange.cancel_order(symbol='1000' + symbol.upper() + 'USDT', orderId=order_id)
            return {
                'order_id': order_id,
                'symbol': '1000' + symbol,
                'status': 'canceled' if cancel_order_info['status'] == 'CANCELED' else 'failed',
            }
        except:
            print(format_exc())
            return None
        
    # 设置杠杆(默认3倍)(TODO:暂未统一到其他交易所)
    def change_swap_leverage_kcoin(self, symbol, leverage=3):
        """
        设置杠杆
        @param symbol:  btc
        @param leverage: 杠杆倍数
        @return:
            {'symbol': '1000BONKUSDT', 'leverage': '5', 'maxNotionalValue': '500000'}
        """
        try:
            return self.swap_exchange.change_leverage(symbol='1000' + symbol.upper() + 'USDT', leverage=leverage)
        except:
            print(format_exc())
            return None


    # endregion =======================================================小额报价的meme coin

    # endregion =======================================================usdt swap

    # TODO：币本位合约
    # region =======================================coin swap

    # endregion =======================================coin swap

    # region ==============================================borrow/lending
    def _make_signed_request(self, endpoint, params=None):
        """
        创建签名请求
        """
        if params is None:
            params = {}

        # 添加时间戳
        params['timestamp'] = int(time() * 1000)
        params['recvWindow'] = 60000

        # 创建查询字符串
        query_string = urlencode(params)

        # 创建签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # 添加签名到参数
        params['signature'] = signature

        # 创建headers
        headers = {
            'X-MBX-APIKEY': self.api_key
        }

        # 发送请求
        url = f"https://api.binance.com{endpoint}"
        response = requests.get(url, params=params, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            print(f"API请求失败: {response.status_code}, {response.text}")
            return None

    # 质押借币利率
    def get_loan_interest_rate(self, token='USDT'):
        """
        获取借币利率与限额
        :param token: 币种，如USDT、USDC等
        :return: 借币利率和限额信息
        """
        try:
            response = self.crypto_loan_client.rest_api.get_flexible_loan_assets_data(
                loan_coin=token.upper() if token else None
            )
            data = response.data()
            if hasattr(data, 'rows') and data.rows:
                return float(data.rows[0].flexible_interest_rate)
            return None
        except Exception:
            print(format_exc())
            return None

    # TODO:查询质押借币借款中订单
    def get_loan_ongoing_orders(self, loan_coin=None, collateral_coin=None):
        """
        查询质押借币借款中订单
        :param loan_coin: 借币币种，如USDT、USDC等
        :param collateral_coin: 抵押币种，如BTC、ETH等
        :return: pandas DataFrame格式的质押借币订单数据
        
        """
        try:
            response = self.crypto_loan_client.rest_api.get_flexible_loan_ongoing_orders(
                loan_coin=loan_coin.upper() if loan_coin else None,
                collateral_coin=collateral_coin.upper() if collateral_coin else None
            )
            data = response.data()
            if hasattr(data, 'rows') and data.rows:
                return data.rows
            return None
        except Exception:
            print(format_exc())
            return None
        
    # TODO：获取质押借币计息记录(TODO:官方暂时没有接口)
    def get_interest_accrued(self, token=None, after=None, before=None,
                           loan_type='flexible', limit=100):
        """
        获取利息记录，支持普通借贷和VIP借贷
        :param token: 币种
        :param after: 请求此时间之后的数据 (时间字符串)
        :param before: 请求此时间之前的数据 (时间字符串)
        :param loan_type: 借贷类型，'flexible'为普通借贷，'vip'为VIP借贷
        :param limit: 每页数量，默认100，最大100
        :return: pandas DataFrame格式的利息记录
        """
        try:
            pass
        except Exception:
            print(format_exc())
            return None

    # 查询VIP借币借款中订单
    def get_vip_loan_ongoing_orders(self, order_id=None, collateral_account_id=None,
                                   loan_coin=None, collateral_coin=None,
                                   current=1, limit=10):
        """
        查询VIP借币借款中订单
        VIP 借币仅对 VIP 用户开放

        :param order_id: 订单ID
        :param collateral_account_id: 抵押账户ID
        :param loan_coin: 借币币种，如USDT、USDC等
        :param collateral_coin: 抵押币种，如BTC、ETH等
        :param current: 当前查询页数，从1开始，默认值：1，最大：1000
        :param limit: 每页返回数量，默认值：10，最大：100
        :return: pandas DataFrame格式的VIP借币订单数据
        """
        try:
            params = {
                'current': current,
                'limit': limit
            }

            if order_id:
                params['orderId'] = order_id
            if collateral_account_id:
                params['collateralAccountId'] = collateral_account_id
            if loan_coin:
                params['loanCoin'] = loan_coin.upper()
            if collateral_coin:
                params['collateralCoin'] = collateral_coin.upper()

            result = self._make_signed_request('/sapi/v1/loan/vip/ongoing/orders', params)

            if result and 'rows' in result:
                # 转换数据格式，添加时间处理
                df = pd.DataFrame(result['rows'])
                if not df.empty:
                    # 处理时间字段
                    if 'loanDate' in df.columns:
                        df['loanDate'] = pd.to_numeric(df['loanDate'], errors='coerce')
                        df['loan_datetime'] = pd.to_datetime(df['loanDate'], unit='ms')
                        df['loan_datetime_beijing'] = df['loan_datetime'] + pd.Timedelta(hours=8)

                    if 'expirationTime' in df.columns:
                        df['expirationTime'] = pd.to_numeric(df['expirationTime'], errors='coerce')
                        df['expiration_datetime'] = pd.to_datetime(df['expirationTime'], unit='ms')
                        df['expiration_datetime_beijing'] = df['expiration_datetime'] + pd.Timedelta(hours=8)

                    # 处理数字字段
                    numeric_fields = ['totalDebt', 'loanRate', 'residualInterest',
                                    'totalCollateralValueAfterHaircut', 'lockedCollateralValue', 'currentLTV']
                    for field in numeric_fields:
                        if field in df.columns:
                            df[field] = pd.to_numeric(df[field], errors='coerce')
                # 去除不需要的列，collateralAccountId，collateralCoin
                df = df.drop(columns=['collateralAccountId', 'collateralCoin'])
                return df

            # 如果没有数据，返回空的DataFrame
            return pd.DataFrame()

        except Exception:
            print(format_exc())
            return pd.DataFrame()

    # 查询VIP借币还款记录历史
    def get_vip_loan_repayment_history(self, order_id=None, loan_coin=None,
                                      start_time=None, end_time=None,
                                      current=1, limit=100, auto_loop=True):
        """
        查询VIP借币还款记录历史
        VIP 借币仅对 VIP 用户开放，支持循环获取所有数据

        :param order_id: 订单ID
        :param loan_coin: 借币币种，如USDT、USDC等
        :param start_time: 开始时间 (北京时间字符串或时间戳)，如'2024-01-01'或'2024-01-01 10:30:00'
        :param end_time: 结束时间 (北京时间字符串或时间戳)，如果未指定则使用当前北京时间
        :param current: 当前查询页数，从1开始，默认值：1，最大：1000
        :param limit: 每页返回数量，默认值：100，最大：100
        :param auto_loop: 是否自动循环获取所有数据，默认True
        :return: pandas DataFrame格式的VIP借币还款历史记录

        注意：
        - 输入的时间参数假设为北京时间，函数内部会自动转换为UTC时间戳
        - 如果没有发送 startTime 和 endTime，默认返回最近 90 天的数据
        - startTime 和 endTime 的最大间隔为 180 天
        - 当auto_loop=True时，会自动分段查询并合并所有数据
        - 返回的DataFrame中包含北京时间字段 'repay_datetime_beijing'
        """
        try:
            # 处理结束时间（假设输入为北京时间）
            if end_time is None:
                end_time = pd.Timestamp.now(tz='Asia/Shanghai')  # 当前北京时间
            elif isinstance(end_time, str):
                # 字符串时间假设为北京时间
                end_time = pd.to_datetime(end_time, utc=False)
                if end_time.tz is None:
                    end_time = end_time.tz_localize('Asia/Shanghai')
            elif isinstance(end_time, (int, float)):
                # 时间戳假设为UTC，转换为北京时间
                end_time = pd.to_datetime(end_time, unit='ms', utc=True).tz_convert('Asia/Shanghai')

            # 处理开始时间（假设输入为北京时间）
            if start_time is None:
                start_time = end_time - pd.Timedelta(days=90)
            elif isinstance(start_time, str):
                # 字符串时间假设为北京时间
                start_time = pd.to_datetime(start_time, utc=False)
                if start_time.tz is None:
                    start_time = start_time.tz_localize('Asia/Shanghai')
            elif isinstance(start_time, (int, float)):
                # 时间戳假设为UTC，转换为北京时间
                start_time = pd.to_datetime(start_time, unit='ms', utc=True).tz_convert('Asia/Shanghai')

            # 确保两个时间都有时区信息
            if start_time.tz is None:
                start_time = start_time.tz_localize('Asia/Shanghai')
            if end_time.tz is None:
                end_time = end_time.tz_localize('Asia/Shanghai')

            # 如果不需要自动循环或时间间隔小于180天，直接查询
            if not auto_loop or (end_time - start_time).days <= 180:
                return self._get_single_vip_repayment_batch(
                    order_id, loan_coin, start_time, end_time, current, limit
                )
            # 自动循环获取所有数据
            all_data = []
            current_start = start_time
            max_interval = pd.Timedelta(days=180)

            # print(f"开始循环获取VIP还款记录: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}")
            while current_start < end_time:
                # 计算当前批次的结束时间
                current_end = min(current_start + max_interval, end_time)

                # print(f"正在获取: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")

                # 获取当前批次的数据
                batch_df = self._get_single_vip_repayment_batch(
                    order_id, loan_coin, current_start, current_end, 1, limit
                )

                if batch_df is not None and not batch_df.empty:
                    all_data.append(batch_df)
                    # print(f"  获取到 {len(batch_df)} 条记录")
                else:
                    print("  无数据")

                # 移动到下一个时间段
                current_start = current_end

                # 添加小延迟避免API限制
                sleep(0.1)

            # 合并所有数据
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)

                # 去重（基于所有字段）
                combined_df = combined_df.drop_duplicates().reset_index(drop=True)

                # 按时间排序（最新的在前）
                if 'repayTime' in combined_df.columns:
                    combined_df = combined_df.sort_values('repayTime', ascending=False).reset_index(drop=True)

                # print(f"✓ 总共获取到 {len(combined_df)} 条VIP还款记录")
                # 去除不需要的列，collateralCoin
                # 如果combined_df是series，则转换为DataFrame
                combined_df = combined_df.drop(columns=['collateralCoin'])
                return combined_df
            else:
                print("未获取到任何VIP还款记录")
                return pd.DataFrame()

        except Exception:
            print(format_exc())
            return pd.DataFrame()

    def _get_single_vip_repayment_batch(self, order_id=None, loan_coin=None,
                                       start_time=None, end_time=None,
                                       current=1, limit=100):
        """
        获取单个批次的VIP还款记录
        """
        try:
            params = {
                'current': current,
                'limit': limit
            }

            if order_id:
                params['orderId'] = order_id
            if loan_coin:
                params['loanCoin'] = loan_coin.upper()
            if start_time:
                if isinstance(start_time, pd.Timestamp):
                    # 如果有时区信息，转换为UTC时间戳
                    if start_time.tz is not None:
                        params['startTime'] = int(start_time.tz_convert('UTC').timestamp() * 1000)
                    else:
                        params['startTime'] = int(start_time.timestamp() * 1000)
                elif isinstance(start_time, str):
                    # 字符串时间假设为北京时间，转换为UTC
                    beijing_time = pd.to_datetime(start_time).tz_localize('Asia/Shanghai')
                    params['startTime'] = int(beijing_time.tz_convert('UTC').timestamp() * 1000)
                else:
                    params['startTime'] = int(start_time)
            if end_time:
                if isinstance(end_time, pd.Timestamp):
                    # 如果有时区信息，转换为UTC时间戳
                    if end_time.tz is not None:
                        params['endTime'] = int(end_time.tz_convert('UTC').timestamp() * 1000)
                    else:
                        params['endTime'] = int(end_time.timestamp() * 1000)
                elif isinstance(end_time, str):
                    # 字符串时间假设为北京时间，转换为UTC
                    beijing_time = pd.to_datetime(end_time).tz_localize('Asia/Shanghai')
                    params['endTime'] = int(beijing_time.tz_convert('UTC').timestamp() * 1000)
                else:
                    params['endTime'] = int(end_time)

            result = self._make_signed_request('/sapi/v1/loan/vip/repay/history', params)

            if result and 'rows' in result:
                # 转换数据格式，添加时间处理
                df = pd.DataFrame(result['rows'])
                if not df.empty:
                    # 处理时间字段
                    if 'loanDate' in df.columns:
                        df['loanDate'] = pd.to_numeric(df['loanDate'], errors='coerce')
                        df['loan_datetime'] = pd.to_datetime(df['loanDate'], unit='ms')
                        df['loan_datetime_beijing'] = df['loan_datetime'] + pd.Timedelta(hours=8)

                    if 'repayTime' in df.columns:
                        df['repayTime'] = pd.to_numeric(df['repayTime'], errors='coerce')
                        df['repay_datetime'] = pd.to_datetime(df['repayTime'], unit='ms')
                        df['repay_datetime_beijing'] = df['repay_datetime'] + pd.Timedelta(hours=8)

                    # 处理数字字段
                    numeric_fields = ['repayAmount']
                    for field in numeric_fields:
                        if field in df.columns:
                            df[field] = pd.to_numeric(df[field], errors='coerce')
                df = df.drop(columns=['collateralCoin'])
                return df

            # 如果没有数据，返回空的DataFrame
            return pd.DataFrame()

        except Exception:
            print(format_exc())
            return pd.DataFrame()

    # 查询VIP借币利息记录
    def get_vip_loan_accrued_interest(self, order_id=None, token='USDT',
                                     start_time=None, end_time=None,
                                     current=1, limit=100, auto_loop=True):
        """
        查询VIP借币利息记录
        查询历史累计计息记录，支持循环获取所有数据

        :param order_id: 订单ID
        :param token: 借币币种，如USDT、USDC等
        :param start_time: 开始时间 (北京时间字符串或时间戳)，如'2024-01-01'或'2024-01-01 10:30:00'
        :param end_time: 结束时间 (北京时间字符串或时间戳)，如果未指定则使用当前北京时间
        :param current: 当前查询页数，从1开始，默认值：1，最大：1000
        :param limit: 每页返回数量，默认值：100，最大：100
        :param auto_loop: 是否自动循环获取所有数据，默认True
        :return: pandas DataFrame格式的VIP借币利息记录

        注意：
        - 输入的时间参数假设为北京时间，函数内部会自动转换为UTC时间戳
        - 如果没有发送 startTime 和 endTime，默认返回最近 90 天的数据
        - startTime 和 endTime 的最大间隔为 90 天
        - 当auto_loop=True时，会自动分段查询并合并所有数据
        - 返回的DataFrame中包含北京时间字段 'accrual_datetime_beijing'
        """
        try:
            # 处理结束时间（假设输入为北京时间）
            if end_time is None:
                end_time = pd.Timestamp.now(tz='Asia/Shanghai')  # 当前北京时间
            elif isinstance(end_time, str):
                # 字符串时间假设为北京时间
                end_time = pd.to_datetime(end_time, utc=False)
                if end_time.tz is None:
                    end_time = end_time.tz_localize('Asia/Shanghai')
            elif isinstance(end_time, (int, float)):
                # 时间戳假设为UTC，转换为北京时间
                end_time = pd.to_datetime(end_time, unit='ms', utc=True).tz_convert('Asia/Shanghai')

            # 处理开始时间（假设输入为北京时间）
            if start_time is None:
                start_time = end_time - pd.Timedelta(days=90)
            elif isinstance(start_time, str):
                # 字符串时间假设为北京时间
                start_time = pd.to_datetime(start_time, utc=False)
                if start_time.tz is None:
                    start_time = start_time.tz_localize('Asia/Shanghai')
            elif isinstance(start_time, (int, float)):
                # 时间戳假设为UTC，转换为北京时间
                start_time = pd.to_datetime(start_time, unit='ms', utc=True).tz_convert('Asia/Shanghai')

            # 确保两个时间都有时区信息
            if start_time.tz is None:
                start_time = start_time.tz_localize('Asia/Shanghai')
            if end_time.tz is None:
                end_time = end_time.tz_localize('Asia/Shanghai')

            # 如果不需要自动循环或时间间隔小于90天，直接查询
            if not auto_loop or (end_time - start_time).days <= 90:
                return self._get_single_vip_interest_batch(
                    order_id, token, start_time, end_time, current, limit
                )

            # 自动循环获取所有数据
            all_data = []
            current_start = start_time
            max_interval = pd.Timedelta(days=90)

            print(f"开始循环获取VIP利息记录: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}")

            while current_start < end_time:
                # 计算当前批次的结束时间
                current_end = min(current_start + max_interval, end_time)

                print(f"正在获取: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")

                # 获取当前批次的数据
                batch_df = self._get_single_vip_interest_batch(
                    order_id, token, current_start, current_end, 1, limit
                )

                if batch_df is not None and not batch_df.empty:
                    all_data.append(batch_df)
                    print(f"  获取到 {len(batch_df)} 条记录")
                else:
                    print(f"  无数据")

                # 移动到下一个时间段
                current_start = current_end

                # 添加小延迟避免API限制
                from time import sleep
                sleep(0.1)

            # 合并所有数据
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)

                # 去重（基于所有字段）
                combined_df = combined_df.drop_duplicates().reset_index(drop=True)

                # 按时间排序（最新的在前）
                if 'accrualTime' in combined_df.columns:
                    combined_df = combined_df.sort_values('accrualTime', ascending=False).reset_index(drop=True)

                print(f"✓ 总共获取到 {len(combined_df)} 条VIP利息记录")
                return combined_df
            else:
                print("未获取到任何VIP利息记录")
                return pd.DataFrame()

        except Exception:
            print(format_exc())
            return pd.DataFrame()

    def _get_single_vip_interest_batch(self, order_id=None, token=None,
                                      start_time=None, end_time=None,
                                      current=1, limit=100):
        """
        获取单个批次的VIP利息记录 
        """
        try:
            params = {
                'current': current,
                'limit': limit
            }

            if order_id:
                params['orderId'] = order_id
            if token:
                params['loanCoin'] = token.upper()
            if start_time:
                if isinstance(start_time, pd.Timestamp):
                    # 如果有时区信息，转换为UTC时间戳
                    if start_time.tz is not None:
                        params['startTime'] = int(start_time.tz_convert('UTC').timestamp() * 1000)
                    else:
                        params['startTime'] = int(start_time.timestamp() * 1000)
                elif isinstance(start_time, str):
                    # 字符串时间假设为北京时间，转换为UTC
                    beijing_time = pd.to_datetime(start_time).tz_localize('Asia/Shanghai')
                    params['startTime'] = int(beijing_time.tz_convert('UTC').timestamp() * 1000)
                else:
                    params['startTime'] = int(start_time)
            if end_time:
                if isinstance(end_time, pd.Timestamp):
                    # 如果有时区信息，转换为UTC时间戳
                    if end_time.tz is not None:
                        params['endTime'] = int(end_time.tz_convert('UTC').timestamp() * 1000)
                    else:
                        params['endTime'] = int(end_time.timestamp() * 1000)
                elif isinstance(end_time, str):
                    # 字符串时间假设为北京时间，转换为UTC
                    beijing_time = pd.to_datetime(end_time).tz_localize('Asia/Shanghai')
                    params['endTime'] = int(beijing_time.tz_convert('UTC').timestamp() * 1000)
                else:
                    params['endTime'] = int(end_time)

            result = self._make_signed_request('/sapi/v1/loan/vip/accruedInterest', params)

            if result and 'rows' in result:
                # 转换数据格式，添加时间处理
                df = pd.DataFrame(result['rows'])
                if not df.empty:
                    # 处理时间字段
                    if 'accrualTime' in df.columns:
                        df['accrualTime'] = pd.to_numeric(df['accrualTime'], errors='coerce')
                        df['accrual_datetime'] = pd.to_datetime(df['accrualTime'], unit='ms')
                        df['accrual_datetime_beijing'] = df['accrual_datetime'] + pd.Timedelta(hours=8)

                    # 处理数字字段
                    numeric_fields = ['principalAmount', 'interestAmount', 'annualInterestRate']
                    for field in numeric_fields:
                        if field in df.columns:
                            df[field] = pd.to_numeric(df[field], errors='coerce')

                return df

            # 如果没有数据，返回空的DataFrame
            return pd.DataFrame()

        except Exception:
            print(format_exc())
            return pd.DataFrame()
    # endregion ==============================================borrow/lending