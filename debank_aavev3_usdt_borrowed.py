#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import sys
import time
from decimal import Decimal, getcontext
from typing import Any, Dict, List, Optional
from urllib.error import HTTPError, URLError
from urllib.parse import urlencode
from urllib.request import Request, urlopen
from icecream import ic
try:
    import requests  # type: ignore
except Exception:  # pragma: no cover
    requests = None  # type: ignore

DEFAULT_ADDRESS = "0x97ddf8b54b755de43eb71361eea0f056e619a922"

# High precision for token amounts
getcontext().prec = 28


def http_get_json(
    url: str,
    params: Dict[str, Any],
    timeout: int = 12,
    retries: int = 3,
) -> Dict[str, Any]:
    query = urlencode(params)
    full_url = f"{url}?{query}" if params else url

    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/124.0 Safari/537.36"
        ),
        "Accept": "application/json, text/plain, */*",
        "Referer": "https://debank.com/",
        "Origin": "https://debank.com",
        "Connection": "keep-alive",
        "Accept-Language": "en-US,en;q=0.9",
    }

    last_exc: Optional[Exception] = None
    for attempt in range(retries):
        try:
            req = Request(full_url, headers=headers, method="GET")
            with urlopen(req, timeout=timeout) as resp:
                data = resp.read()
                return json.loads(data.decode("utf-8"))
        except (
            HTTPError,
            URLError,
            TimeoutError,
            json.JSONDecodeError,
        ) as exc:
            last_exc = exc
            # Exponential backoff
            time.sleep(0.6 * (2 ** attempt))

    if last_exc:
        raise last_exc
    return {}


def extract_portfolio_items(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    data = payload.get("data") or {}
    # Debank responses sometimes use one of these keys
    for key in (
        "portfolio_item_list",
        "portfolio_list",
        "item_list",
        "portfolio",
    ):  # type: ignore
        items = data.get(key)
        if isinstance(items, list):
            return items
        # some responses nest under data -> data
        if isinstance(data.get("data"), dict):
            nested = data["data"].get(key)
            if isinstance(nested, list):
                return nested
    return []


def sum_usdt_borrowed_eth(items: List[Dict[str, Any]]) -> Decimal:
    total = Decimal("0")
    for item in items:
        item_chain = (item.get("chain") or "").lower()
        detail = item.get("detail") or item.get("portfolio_detail") or {}

        # Borrowed tokens list naming varies
        token_lists = [
            detail.get("borrow_token_list"),
            detail.get("debt_token_list"),
            detail.get("debt_list"),
        ]

        for token_list in token_lists:
            if not isinstance(token_list, list):
                continue
            for token in token_list:
                if not isinstance(token, dict):
                    continue
                symbol = str(token.get("symbol") or "").upper()
                token_chain = (token.get("chain") or item_chain or "").lower()
                if token_chain != "eth":
                    continue
                if symbol != "USDT":
                    continue
                amount_val = token.get("amount")
                if amount_val is None:
                    continue
                try:
                    amount = Decimal(str(amount_val))
                except Exception:
                    continue
                total += amount
    return total


def fetch_from_project_endpoint(address: str) -> Optional[Decimal]:
    print("fetch_from_project_endpoint")
    url = "https://api.debank.com/portfolio/project"
    params = {"user_addr": address, "id": "aave_v3"}
    payload = http_get_json(url, params)
    ic(payload)
    items = extract_portfolio_items(payload)
    ic(items)
    if not items:
        return None
    return sum_usdt_borrowed_eth(items)


def fetch_from_complex_protocol_list(address: str) -> Optional[Decimal]:
    print("fetch_from_complex_protocol_list")
    url = "https://api.debank.com/user/complex_protocol_list"
    params = {"id": address}
    payload = http_get_json(url, params)

    data = payload.get("data")
    if not isinstance(data, list):
        return None

    # Find Aave V3 entry heuristically
    for proto in data:
        if not isinstance(proto, dict):
            continue
        pid = str(proto.get("id") or "").lower()
        name = str(proto.get("name") or "").lower()
        if ("aave" in pid or "aave" in name) and ("v3" in pid or "v3" in name):
            items = (
                proto.get("portfolio_item_list")
                or proto.get("portfolio")
                or []
            )
            if isinstance(items, list):
                return sum_usdt_borrowed_eth(items)
    return None


def format_decimal_plain(d: Decimal) -> str:
    s = format(d, "f")
    if "." in s:
        s = s.rstrip("0").rstrip(".")
    return s or "0"


def graphql_post(
    url: str,
    query: str,
    variables: Dict[str, Any],
) -> Dict[str, Any]:
    if requests is None:
        raise RuntimeError(
            "requests is required for subgraph queries"
        )
    resp = requests.post(
        url,
        json={"query": query, "variables": variables},
        timeout=20,
        headers={"content-type": "application/json"},
    )
    resp.raise_for_status()
    return resp.json()


def fetch_aave_v3_all_info_via_subgraph(
    address: str,
) -> Dict[str, Any]:
    # Allow override for environments that require gateway API keys
    subgraph_url = os.environ.get(
        "AAVE_V3_SUBGRAPH_URL",
        (
            "https://api.thegraph.com/subgraphs/name/"
            "aave/protocol-v3"
        ),  # default public
    )

    query = (
        "query($user:String!){\n"
        "  userReserves(where:{user:$user}){\n"
        "    user\n"
        "    reserve{symbol name decimals id underlyingAsset}\n"
        "    usageAsCollateralEnabledOnUser\n"
        "    scaledATokenBalance\n"
        "    currentATokenBalance\n"
        "    currentStableDebt\n"
        "    currentVariableDebt\n"
        "    currentTotalDebt\n"
        "    principalStableDebt\n"
        "    principalVariableDebt\n"
        "    liquidityRate\n"
        "    stableBorrowRate\n"
        "    variableBorrowRate\n"
        "  }\n"
        "}"
    )

    variables = {"user": address.lower()}
    data = graphql_post(subgraph_url, query, variables)
    result: Dict[str, Any] = {
        "user": address,
        "network": "ethereum",
        "reserves": [],
    }

    user_reserves = (data.get("data") or {}).get("userReserves") or []
    if not isinstance(user_reserves, list):
        return result

    for ur in user_reserves:
        reserve = ur.get("reserve") or {}
        item = {
            "symbol": reserve.get("symbol"),
            "name": reserve.get("name"),
            "decimals": reserve.get("decimals"),
            "underlyingAsset": (
                reserve.get("underlyingAsset") or reserve.get("id")
            ),
            "usageAsCollateralEnabledOnUser": ur.get(
                "usageAsCollateralEnabledOnUser"
            ),
            "scaledATokenBalance": ur.get("scaledATokenBalance"),
            "currentATokenBalance": ur.get("currentATokenBalance"),
            "currentStableDebt": ur.get("currentStableDebt"),
            "currentVariableDebt": ur.get("currentVariableDebt"),
            "currentTotalDebt": ur.get("currentTotalDebt"),
            "principalStableDebt": ur.get("principalStableDebt"),
            "principalVariableDebt": ur.get("principalVariableDebt"),
            "liquidityRate": ur.get("liquidityRate"),
            "stableBorrowRate": ur.get("stableBorrowRate"),
            "variableBorrowRate": ur.get("variableBorrowRate"),
        }
        result["reserves"].append(item)

    return result


def main() -> None:
    address = (sys.argv[1] if len(sys.argv) > 1 else DEFAULT_ADDRESS).strip()
    if not address:
        print("{}")
        return
    ic(address)

    # Preferred: Aave v3 subgraph for full info (ETH only)
    try:
        full_info = fetch_aave_v3_all_info_via_subgraph(address)
        print(json.dumps(full_info, ensure_ascii=False))
        return
    except Exception:
        pass

    # Fallback to Debank (may fail due to API protections)
    amount: Optional[Decimal] = None
    try:
        amount = fetch_from_project_endpoint(address)
    except Exception:
        amount = None
    if amount is None:
        try:
            amount = fetch_from_complex_protocol_list(address)
        except Exception:
            amount = None
    if amount is None:
        print(
            json.dumps(
                {"user": address, "network": "ethereum", "reserves": []},
                ensure_ascii=False,
            )
        )
        return
    # If only Debank numeric was obtained, wrap minimally as JSON
    print(
        json.dumps(
            {
                "user": address,
                "network": "ethereum",
                "reserves": [
                    {
                        "symbol": "USDT",
                        "currentTotalDebt": format_decimal_plain(amount),
                    }
                ],
            },
            ensure_ascii=False,
        )
    )


if __name__ == "__main__":
    main()
